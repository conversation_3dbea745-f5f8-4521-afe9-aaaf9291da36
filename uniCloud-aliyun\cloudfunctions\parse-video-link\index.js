"use strict";

/**
 * 视频解析云函数
 * 基于 parse-video-py 项目实现
 * 支持抖音和小红书视频/图集解析
 */

const db = uniCloud.database();

// 简单的内存缓存，用于缓存TikTok解析结果
const tiktokCache = new Map();
const CACHE_TTL = 10 * 60 * 1000; // 10分钟缓存时间

/**
 * 获取缓存的TikTok解析结果
 * @param {string} url TikTok URL
 * @returns {Object|null} 缓存的结果或null
 */
function getCachedTiktokResult(url) {
  const cached = tiktokCache.get(url);
  if (cached && Date.now() - cached.timestamp < CACHE_TTL) {
    console.log("使用缓存的TikTok解析结果");
    return cached.data;
  }
  return null;
}

/**
 * 缓存TikTok解析结果
 * @param {string} url TikTok URL
 * @param {Object} data 解析结果
 */
function cacheTiktokResult(url, data) {
  tiktokCache.set(url, {
    data: data,
    timestamp: Date.now()
  });

  // 清理过期缓存
  if (tiktokCache.size > 100) { // 限制缓存大小
    const now = Date.now();
    for (const [key, value] of tiktokCache.entries()) {
      if (now - value.timestamp > CACHE_TTL) {
        tiktokCache.delete(key);
      }
    }
  }
}



/**
 * 获取默认请求头
 * @param {string} platform 平台类型 'douyin' | 'redbook' | 'tiktok'
 * @returns {Object} 请求头对象
 */
function getDefaultHeaders(platform = 'douyin') {
  if (platform === 'redbook') {
    // 小红书使用 Windows 设备的 User-Agent
    const windowsUserAgents = [
      "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
      "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
      "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ];
    const randomUserAgent = windowsUserAgents[Math.floor(Math.random() * windowsUserAgents.length)];
    return {
      "User-Agent": randomUserAgent
    };
  } else if (platform === 'tiktok') {
    // TikTok使用移动端 User-Agent，模拟手机浏览器访问
    const mobileUserAgents = [
      "Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1",
      "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1",
      "Mozilla/5.0 (Linux; Android 13; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36",
      "Mozilla/5.0 (Linux; Android 12; Pixel 6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"
    ];
    const randomUserAgent = mobileUserAgents[Math.floor(Math.random() * mobileUserAgents.length)];
    return {
      "User-Agent": randomUserAgent,
      "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8",
      "Accept-Language": "en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7",
      "Accept-Encoding": "gzip, deflate, br",
      "Referer": "https://www.tiktok.com/",
      "Sec-Fetch-Dest": "document",
      "Sec-Fetch-Mode": "navigate",
      "Sec-Fetch-Site": "same-origin"
    };
  } else {
    // 抖音使用 iOS 设备的 User-Agent
    const iosUserAgents = [
      "Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1",
      "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1",
      "Mozilla/5.0 (iPhone; CPU iPhone OS 15_7 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.6 Mobile/15E148 Safari/604.1"
    ];
    const randomUserAgent = iosUserAgents[Math.floor(Math.random() * iosUserAgents.length)];
    return {
      "User-Agent": randomUserAgent
    };
  }
}

/**
 * 根据视频ID构建请求URL
 * @param {string} videoId 视频ID
 * @returns {string} 请求URL
 */
function getRequestUrlByVideoId(videoId) {
  return `https://www.iesdouyin.com/share/video/${videoId}/`;
}

/**
 * 获取视频重定向URL
 * @param {string} videoUrl 原始视频URL
 * @returns {Promise<string>} 重定向后的URL
 */
async function getVideoRedirectUrl(videoUrl) {
  try {
    const response = await uniCloud.httpclient.request(videoUrl, {
      method: "GET",
      headers: getDefaultHeaders('douyin'),
      followRedirect: false,
      timeout: 10000
    });

    // 返回重定向后的地址，如果没有重定向则返回原地址(抖音中的西瓜视频,重定向地址为空)
    return response.headers.location || videoUrl;
  } catch (error) {
    console.warn("获取重定向URL失败:", error.message);
    return videoUrl;
  }
}

/**
 * 解析分享URL获取视频信息
 * @param {string} shareUrl 分享URL
 * @returns {Promise<Object>} 视频信息
 */
async function parseShareUrl(shareUrl) {
  console.log("开始解析分享URL:", shareUrl);
  
  let requestUrl = shareUrl;
  
  if (shareUrl.startsWith("https://www.douyin.com/video/")) {
    // 支持电脑网页版链接 https://www.douyin.com/video/xxxxxx
    const videoId = shareUrl.strip ? shareUrl.strip("/").split("/").pop() : shareUrl.replace(/\/$/, "").split("/").pop();
    requestUrl = getRequestUrlByVideoId(videoId);
  } else if (shareUrl.startsWith("https://www.iesdouyin.com/share/video/")) {
    // 支持 iesdouyin.com 直接链接，提取视频ID并构建标准请求URL
    const videoId = shareUrl.split("?")[0].replace(/\/$/, "").split("/").pop();
    requestUrl = getRequestUrlByVideoId(videoId);
  } else {
    // 支持app分享链接 https://v.douyin.com/xxxxxx
    const shareResponse = await uniCloud.httpclient.request(shareUrl, {
      method: "GET",
      headers: getDefaultHeaders('douyin'),
      followRedirect: false,
      timeout: 15000
    });

    const location = shareResponse.headers.location;
    if (!location) {
      throw new Error("无法获取重定向URL");
    }

    const videoId = location.split("?")[0].replace(/\/$/, "").split("/").pop();
    requestUrl = getRequestUrlByVideoId(videoId);
  }

  console.log("请求URL:", requestUrl);

  // 获取视频页面内容
  const response = await uniCloud.httpclient.request(requestUrl, {
    method: "GET",
    headers: getDefaultHeaders('douyin'),
    followRedirect: true,
    timeout: 15000
  });

  if (response.status !== 200) {
    throw new Error(`HTTP请求失败，状态码: ${response.status}`);
  }

  const htmlContent = response.data;
  console.log("HTML内容长度:", htmlContent.length);

  // 使用正则表达式查找 window._ROUTER_DATA
  const pattern = /window\._ROUTER_DATA\s*=\s*(.*?)<\/script>/s;
  const match = pattern.exec(htmlContent);

  if (!match || !match[1]) {
    throw new Error("parse video json info from html fail");
  }

  let jsonData;
  try {
    jsonData = JSON.parse(match[1].trim());
  } catch (error) {
    throw new Error("JSON数据解析失败");
  }
  
  // 获取链接返回json数据进行视频和图集判断,如果指定类型不存在，抛出异常
  // 返回的json数据中，视频字典类型为 video_(id)/page
  const VIDEO_ID_PAGE_KEY = "video_(id)/page";
  // 返回的json数据中，视频字典类型为 note_(id)/page
  const NOTE_ID_PAGE_KEY = "note_(id)/page";
  
  let originalVideoInfo;
  
  if (VIDEO_ID_PAGE_KEY in jsonData.loaderData) {
    originalVideoInfo = jsonData.loaderData[VIDEO_ID_PAGE_KEY].videoInfoRes;
  } else if (NOTE_ID_PAGE_KEY in jsonData.loaderData) {
    originalVideoInfo = jsonData.loaderData[NOTE_ID_PAGE_KEY].videoInfoRes;
  } else {
    throw new Error("failed to parse Videos or Photo Gallery info from json");
  }
  
  // 如果没有视频信息，获取并抛出异常
  if (!originalVideoInfo.item_list || originalVideoInfo.item_list.length === 0) {
    let errDetailMsg = "failed to parse video info from HTML";

    // 添加更详细的调试信息
    console.log("originalVideoInfo 结构:", JSON.stringify(originalVideoInfo, null, 2));

    // 检查 filter_list 中的过滤原因
    if (originalVideoInfo.filter_list && originalVideoInfo.filter_list.length > 0) {
      const filterInfo = originalVideoInfo.filter_list[0];
      const filterReason = filterInfo.filter_reason;
      const detailMsg = filterInfo.detail_msg;

      console.log("抖音返回的错误信息:", detailMsg);
      console.log("过滤原因:", filterReason);

      // 根据过滤原因提供具体的错误信息
      switch (filterReason) {
        case "status_audit_not_pass":
          errDetailMsg = "视频未通过审核或已被下架";
          break;
        case "status_private":
          errDetailMsg = "视频为私密视频，无法访问";
          break;
        case "status_delete":
          errDetailMsg = "视频已被删除";
          break;
        case "status_ban":
          errDetailMsg = "视频已被封禁";
          break;
        default:
          errDetailMsg = detailMsg || `视频无法访问 (原因: ${filterReason})`;
      }
    }

    // 检查是否是因为视频被删除或私有
    if (originalVideoInfo.status_code) {
      console.log("状态码:", originalVideoInfo.status_code);
      if (originalVideoInfo.status_code === 4003004) {
        errDetailMsg = "视频不存在或已被删除";
      } else if (originalVideoInfo.status_code === 4003003) {
        errDetailMsg = "视频为私密视频，无法访问";
      }
    }

    throw new Error(errDetailMsg);
  }
  
  const data = originalVideoInfo.item_list[0];
  
  // 获取图集图片地址
  const images = [];
  // 如果data含有 images，并且 images 是一个列表
  if ("images" in data && Array.isArray(data.images)) {
    // 获取每个图片的url_list中的第一个元素，非空时添加到images列表中
    for (const img of data.images) {
      if (
        "url_list" in img &&
        Array.isArray(img.url_list) &&
        img.url_list.length > 0 &&
        img.url_list[0].length > 0
      ) {
        images.push({
          url: img.url_list[0]
        });
      }
    }
  }
  
  // 获取视频播放地址
  let videoUrl = "";
  if (data.video && data.video.play_addr && data.video.play_addr.url_list && data.video.play_addr.url_list.length > 0) {
    videoUrl = data.video.play_addr.url_list[0].replace("playwm", "play");
  }
  
  // 如果图集地址不为空时，因为没有视频，上面抖音返回的视频地址无法访问，置空处理
  if (images.length > 0) {
    videoUrl = "";
  }
  
  // 获取重定向后的mp4视频地址
  // 图集时，视频地址为空，不处理
  let videoMp4Url = "";
  if (videoUrl.length > 0) {
    videoMp4Url = await getVideoRedirectUrl(videoUrl);
  }
  
  const videoInfo = {
    video_url: videoMp4Url,
    cover_url: data.video.cover.url_list[0],
    title: data.desc,
    images: images,
    author: {
      uid: data.author.sec_uid,
      name: data.author.nickname,
      avatar: data.author.avatar_thumb.url_list[0],
    }
  };
  
  console.log("视频解析完成:", {
    title: videoInfo.title,
    author: videoInfo.author.name,
    hasVideo: !!videoInfo.video_url,
    hasImages: videoInfo.images.length > 0
  });
  
  return videoInfo;
}

/**
 * 从分享文本中提取抖音链接
 * @param {string} shareText 分享文本
 * @returns {string|null} 提取到的抖音链接
 */
function extractDouyinUrl(shareText) {
  console.log("开始提取抖音链接:", shareText);
  
  // 支持多种抖音链接格式
  const patterns = [
    /https?:\/\/v\.douyin\.com\/[A-Za-z0-9_-]+\/?/g,
    /https?:\/\/www\.douyin\.com\/video\/\d+/g,
    /https?:\/\/www\.iesdouyin\.com\/share\/video\/\d+[^?\s]*(\?[^\s]*)?/g  // 修复：支持带查询参数的完整链接
  ];
  
  for (const pattern of patterns) {
    const matches = shareText.match(pattern);
    if (matches && matches.length > 0) {
      const url = matches[0];
      console.log("成功提取抖音链接:", url);
      return url;
    }
  }
  
  console.log("未找到有效的抖音链接");
  return null;
}

/**
 * 从分享文本中提取小红书链接
 * @param {string} shareText 分享文本
 * @returns {string|null} 提取到的小红书链接
 */
function extractRedbookUrl(shareText) {
  console.log("开始提取小红书链接:", shareText);

  // 支持多种小红书链接格式
  const patterns = [
    /https?:\/\/www\.xiaohongshu\.com\/explore\/[A-Za-z0-9]+/g,
    /https?:\/\/www\.xiaohongshu\.com\/discovery\/item\/[A-Za-z0-9]+/g,
    /https?:\/\/xhslink\.com\/[A-Za-z0-9\/]+/g,  // 更宽泛的匹配，包含路径
    /http:\/\/xhslink\.com\/[A-Za-z0-9\/]+/g     // 更宽泛的匹配，包含路径
  ];

  for (const pattern of patterns) {
    const matches = shareText.match(pattern);
    if (matches && matches.length > 0) {
      const url = matches[0];
      console.log("成功提取小红书链接:", url);
      return url;
    }
  }

  console.log("未找到有效的小红书链接");
  return null;
}

/**
 * 从分享文本中提取TikTok链接
 * @param {string} shareText 分享文本
 * @returns {string|null} 提取到的TikTok链接
 */
function extractTiktokUrl(shareText) {
  console.log("开始提取TikTok链接:", shareText);

  // 支持多种TikTok链接格式
  const patterns = [
    // 短链接格式
    /https?:\/\/vm\.tiktok\.com\/[A-Za-z0-9]+\/?/g,
    /https?:\/\/vt\.tiktok\.com\/[A-Za-z0-9]+\/?/g,
    // 网页版链接格式
    /https?:\/\/www\.tiktok\.com\/@[^\/]+\/video\/\d+[^?\s]*(\?[^\s]*)?/g,
    // 移动端链接格式
    /https?:\/\/m\.tiktok\.com\/v\/\d+[^?\s]*(\?[^\s]*)?/g,
    // 其他可能的格式
    /https?:\/\/tiktok\.com\/@[^\/]+\/video\/\d+[^?\s]*(\?[^\s]*)?/g
  ];

  for (const pattern of patterns) {
    const matches = shareText.match(pattern);
    if (matches && matches.length > 0) {
      const url = matches[0];
      console.log("成功提取TikTok链接:", url);
      return url;
    }
  }

  console.log("未找到有效的TikTok链接");
  return null;
}

/**
 * 检测平台类型
 * @param {string} shareText 分享文本
 * @returns {string} 平台类型 'douyin' | 'redbook' | 'tiktok' | 'unknown'
 */
function detectPlatform(shareText) {
  if (shareText.includes("douyin.com") || shareText.includes("抖音")) {
    return "douyin";
  } else if (shareText.includes("xiaohongshu.com") || shareText.includes("xhslink.com") || shareText.includes("小红书")) {
    return "redbook";
  } else if (shareText.includes("tiktok.com") || shareText.includes("TikTok") || shareText.includes("tiktok")) {
    return "tiktok";
  }
  return "unknown";
}

/**
 * 解析小红书分享URL获取内容信息
 * @param {string} shareUrl 分享URL
 * @returns {Promise<Object>} 内容信息
 */
async function parseRedbookShareUrl(shareUrl) {
  console.log("开始解析小红书分享URL:", shareUrl);
  
  // 获取页面内容，设置更高的重定向限制
  const response = await uniCloud.httpclient.request(shareUrl, {
    method: "GET",
    headers: getDefaultHeaders('redbook'),
    followRedirect: true,
    timeout: 15000,
    maxRedirects: 10  // 增加重定向次数限制
  });
  
  if (response.status !== 200) {
    throw new Error(`HTTP请求失败，状态码: ${response.status}`);
  }
  
  const htmlContent = response.data;
  console.log("HTML内容长度:", htmlContent.length);
  
  // 检查是否获取到了小红书的页面
  if (!htmlContent.includes('xiaohongshu') && !htmlContent.includes('小红书')) {
    console.log("页面内容预览:", htmlContent.substring(0, 500));
    throw new Error("未获取到小红书页面内容，可能是链接已失效");
  }
  
  // 使用正则表达式查找 window.__INITIAL_STATE__
  const pattern = /window\.__INITIAL_STATE__\s*=\s*(.*?)<\/script>/s;
  const match = pattern.exec(htmlContent);
  
  if (!match || !match[1]) {
    throw new Error("parse video json info from html fail");
  }
  
  // 解析JSON数据 (需要处理JavaScript语法)
  let jsonData;
  try {
    // 小红书的数据可能包含JavaScript语法，需要处理
    let jsonStr = match[1].trim();
    console.log("原始数据长度:", jsonStr.length);
    console.log("原始数据开头:", jsonStr.substring(0, 200));
    
    // 处理undefined值
    jsonStr = jsonStr.replace(/:\s*undefined/g, ': null');
    
    // 处理函数调用和其他JavaScript语法
    jsonStr = jsonStr.replace(/:\s*function\s*\([^)]*\)\s*\{[^}]*\}/g, ': null');
    
    // 处理正则表达式
    jsonStr = jsonStr.replace(/:\s*\/[^/]+\/[gimuy]*/g, ': null');
    
    // 处理单引号字符串（转换为双引号）
    jsonStr = jsonStr.replace(/:\s*'([^']*)'/g, ': "$1"');
    
    // 处理数字后面的注释
    jsonStr = jsonStr.replace(/(\d+)\s*\/\/[^\n\r]*/g, '$1');
    
    // 处理对象key没有引号的情况
    jsonStr = jsonStr.replace(/([{,]\s*)([a-zA-Z_$][a-zA-Z0-9_$]*)\s*:/g, '$1"$2":');
    
    // 移除JavaScript注释
    jsonStr = jsonStr.replace(/\/\/.*$/gm, '');
    jsonStr = jsonStr.replace(/\/\*[\s\S]*?\*\//g, '');
    
    // 处理尾随逗号
    jsonStr = jsonStr.replace(/,\s*([}\]])/g, '$1');
    
    console.log("处理后数据开头:", jsonStr.substring(0, 200));
    
    jsonData = JSON.parse(jsonStr);
    console.log("JSON解析成功");
  } catch (error) {
    console.error("JSON解析失败:", error.message);
    console.log("解析失败的位置信息:", error.toString());
    
    // 尝试更简单的方法：寻找并提取对象的主要部分
    try {
      let simpleStr = match[1].trim();
      
      // 如果是以{开头，找到对应的}结尾
      let braceCount = 0;
      let validJsonEnd = -1;
      
      for (let i = 0; i < simpleStr.length; i++) {
        if (simpleStr[i] === '{') {
          braceCount++;
        } else if (simpleStr[i] === '}') {
          braceCount--;
          if (braceCount === 0) {
            validJsonEnd = i;
            break;
          }
        }
      }
      
      if (validJsonEnd > 0) {
        simpleStr = simpleStr.substring(0, validJsonEnd + 1);
        console.log("尝试截取有效JSON部分，长度:", simpleStr.length);
        
        // 再次处理
        simpleStr = simpleStr.replace(/:\s*undefined/g, ': null');
        simpleStr = simpleStr.replace(/([{,]\s*)([a-zA-Z_$][a-zA-Z0-9_$]*)\s*:/g, '$1"$2":');
        simpleStr = simpleStr.replace(/,\s*([}\]])/g, '$1');
        
        jsonData = JSON.parse(simpleStr);
        console.log("简化JSON解析成功");
      } else {
        throw new Error("无法找到完整的JSON结构");
      }
    } catch (simpleError) {
      console.error("简化JSON解析也失败:", simpleError.message);
      throw new Error("JSON数据解析失败，页面结构可能已变更");
    }
  }
  
  const noteId = jsonData.note.currentNoteId;
  // 验证返回：小红书的分享链接有有效期，过期后会返回 undefined
  if (noteId === "undefined" || !noteId) {
    throw new Error("parse fail: note id in response is undefined");
  }
  
  const data = jsonData.note.noteDetailMap[noteId].note;
  
  // 视频地址
  let videoUrl = "";
  const h264Data = data?.video?.media?.stream?.h264 || [];
  if (h264Data.length > 0) {
    videoUrl = h264Data[0].masterUrl || "";
  }
  
  // 获取图集图片地址
  const images = [];
  if (!videoUrl && data.imageList) {
    for (const imgItem of data.imageList) {
      // 个别图片有水印, 替换图片域名
      const imageId = imgItem.urlDefault.split("/").pop().split("!")[0];
      // 如果链接中带有 spectrum/ , 替换域名时需要带上
      const spectrumStr = imgItem.urlDefault.includes("spectrum") ? "spectrum/" : "";
      const newUrl = `https://ci.xiaohongshu.com/notes_pre_post/${spectrumStr}${imageId}?imageView2/format/jpg`;
      
      const imgInfo = {
        url: newUrl,
        live_photo_url: ""
      };
      
      // 是否有 livephoto 视频地址
      if (imgItem.livePhoto && imgItem.stream?.h264?.length > 0) {
        imgInfo.live_photo_url = imgItem.stream.h264[0].masterUrl;
      }
      
      images.push(imgInfo);
    }
  }
  
  const contentInfo = {
    video_url: videoUrl,
    cover_url: data.imageList[0].urlDefault,
    title: data.title,
    images: images,
    author: {
      uid: data.user.userId,
      name: data.user.nickname,
      avatar: data.user.avatar,
    }
  };
  
  console.log("小红书内容解析完成:", {
    title: contentInfo.title,
    author: contentInfo.author.name,
    hasVideo: !!contentInfo.video_url,
    hasImages: contentInfo.images.length > 0
  });
  
  return contentInfo;
}

/**
 * TikTok请求重试机制
 * @param {Function} requestFn 请求函数
 * @param {number} maxRetries 最大重试次数
 * @param {number} baseDelay 基础延迟时间(ms)
 * @returns {Promise<any>} 请求结果
 */
async function retryTiktokRequest(requestFn, maxRetries = 3, baseDelay = 1000) {
  let lastError;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`TikTok请求尝试 ${attempt}/${maxRetries}`);
      return await requestFn();
    } catch (error) {
      lastError = error;
      console.warn(`TikTok请求第${attempt}次失败:`, error.message);

      // 如果是最后一次尝试，直接抛出错误
      if (attempt === maxRetries) {
        break;
      }

      // 指数退避延迟
      const delay = baseDelay * Math.pow(2, attempt - 1) + Math.random() * 1000;
      console.log(`等待 ${delay}ms 后重试...`);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  throw lastError;
}

/**
 * 解析TikTok分享URL获取视频信息
 * @param {string} shareUrl 分享URL
 * @returns {Promise<Object>} 视频信息
 */
async function parseTiktokShareUrl(shareUrl) {
  console.log("开始解析TikTok分享URL:", shareUrl);

  // 检查缓存
  const cachedResult = getCachedTiktokResult(shareUrl);
  if (cachedResult) {
    return cachedResult;
  }

  let requestUrl = shareUrl;

  // 处理短链接，需要先获取重定向后的完整URL
  if (shareUrl.includes("vm.tiktok.com") || shareUrl.includes("vt.tiktok.com")) {
    try {
      const redirectResponse = await retryTiktokRequest(async () => {
        return await uniCloud.httpclient.request(shareUrl, {
          method: "GET",
          headers: getDefaultHeaders('tiktok'),
          followRedirect: false,
          timeout: 15000
        });
      }, 2, 500); // 重定向请求使用较少的重试次数

      const location = redirectResponse.headers.location;
      if (location) {
        requestUrl = location;
        console.log("短链接重定向到:", requestUrl);
      }
    } catch (error) {
      console.warn("获取TikTok重定向URL失败:", error.message);
      // 如果重定向失败，继续使用原URL尝试
    }
  }

  // 获取TikTok页面内容，使用重试机制
  const response = await retryTiktokRequest(async () => {
    const resp = await uniCloud.httpclient.request(requestUrl, {
      method: "GET",
      headers: getDefaultHeaders('tiktok'),
      followRedirect: true,
      timeout: 20000,
      maxRedirects: 5
    });

    if (resp.status !== 200) {
      throw new Error(`HTTP请求失败，状态码: ${resp.status}`);
    }

    return resp;
  }, 3, 1500); // 主要请求使用更多重试次数和更长延迟

  const htmlContent = response.data;
  console.log("HTML内容长度:", htmlContent.length);

  // 检查是否获取到了TikTok的页面
  if (!htmlContent.includes('tiktok') && !htmlContent.includes('TikTok')) {
    console.log("页面内容预览:", htmlContent.substring(0, 500));
    throw new Error("未获取到TikTok页面内容，可能是链接已失效或被限制访问");
  }

  // TikTok的数据可能在多个地方，尝试不同的解析方式
  let jsonData = null;

  // 方式1: 尝试解析 __UNIVERSAL_DATA_FOR_REHYDRATION__
  const universalDataPattern = /__UNIVERSAL_DATA_FOR_REHYDRATION__\s*=\s*(.*?)<\/script>/s;
  let match = universalDataPattern.exec(htmlContent);

  if (match && match[1]) {
    try {
      jsonData = JSON.parse(match[1].trim());
      console.log("成功解析 __UNIVERSAL_DATA_FOR_REHYDRATION__ 数据");
    } catch (error) {
      console.warn("解析 __UNIVERSAL_DATA_FOR_REHYDRATION__ 失败:", error.message);
    }
  }

  // 方式2: 尝试解析 window.__INITIAL_SSR_STATE__
  if (!jsonData) {
    const ssrStatePattern = /window\.__INITIAL_SSR_STATE__\s*=\s*(.*?)<\/script>/s;
    match = ssrStatePattern.exec(htmlContent);

    if (match && match[1]) {
      try {
        let jsonStr = match[1].trim();
        // 处理可能的JavaScript语法
        jsonStr = jsonStr.replace(/:\s*undefined/g, ': null');
        jsonData = JSON.parse(jsonStr);
        console.log("成功解析 __INITIAL_SSR_STATE__ 数据");
      } catch (error) {
        console.warn("解析 __INITIAL_SSR_STATE__ 失败:", error.message);
      }
    }
  }

  // 方式3: 尝试从HTML中提取基本信息（降级方案）
  if (!jsonData) {
    console.log("尝试从HTML meta标签中提取基本信息");

    // 提取标题
    const titleMatch = htmlContent.match(/<title[^>]*>(.*?)<\/title>/i);
    const title = titleMatch ? titleMatch[1].replace(/\s*\|\s*TikTok$/, '').trim() : "";

    // 提取描述（可能包含作者信息）
    const descMatch = htmlContent.match(/<meta[^>]*name=["']description["'][^>]*content=["']([^"']*)/i);
    const description = descMatch ? descMatch[1] : "";

    // 提取视频URL（从meta标签）
    const videoUrlMatch = htmlContent.match(/<meta[^>]*property=["']og:video["'][^>]*content=["']([^"']*)/i);
    const videoUrl = videoUrlMatch ? videoUrlMatch[1] : "";

    // 提取封面图
    const coverMatch = htmlContent.match(/<meta[^>]*property=["']og:image["'][^>]*content=["']([^"']*)/i);
    const coverUrl = coverMatch ? coverMatch[1] : "";

    if (title || videoUrl) {
      return {
        video_url: videoUrl,
        cover_url: coverUrl,
        title: title || description,
        images: [],
        author: {
          uid: "",
          name: "TikTok用户",
          avatar: "",
        }
      };
    }

    throw new Error("无法从TikTok页面中提取视频信息，页面结构可能已变更");
  }

  // 解析JSON数据获取视频信息
  let videoInfo = null;

  try {
    // TikTok的数据结构可能会变化，需要尝试不同的路径
    const possiblePaths = [
      'default.webapp.video-detail',
      'default.ItemModule',
      'ItemModule',
      'webapp.video-detail'
    ];

    for (const path of possiblePaths) {
      const pathParts = path.split('.');
      let current = jsonData;

      for (const part of pathParts) {
        if (current && current[part]) {
          current = current[part];
        } else {
          current = null;
          break;
        }
      }

      if (current && current.itemInfo) {
        videoInfo = current.itemInfo.itemStruct;
        console.log("找到视频信息，路径:", path);
        break;
      }
    }

    if (!videoInfo) {
      // 尝试直接查找包含视频信息的对象
      const findVideoInfo = (obj, depth = 0) => {
        if (depth > 5) return null; // 限制递归深度

        if (obj && typeof obj === 'object') {
          // 查找包含视频信息的对象特征
          if (obj.id && obj.desc && obj.video && obj.author) {
            return obj;
          }

          for (const key in obj) {
            const result = findVideoInfo(obj[key], depth + 1);
            if (result) return result;
          }
        }

        return null;
      };

      videoInfo = findVideoInfo(jsonData);
    }

    if (!videoInfo) {
      throw new Error("无法在JSON数据中找到视频信息");
    }

  } catch (error) {
    console.error("解析TikTok JSON数据失败:", error.message);
    throw new Error("TikTok数据解析失败，页面结构可能已变更");
  }

  // 提取视频播放地址
  let videoUrl = "";
  if (videoInfo.video && videoInfo.video.playAddr) {
    videoUrl = videoInfo.video.playAddr;
  } else if (videoInfo.video && videoInfo.video.downloadAddr) {
    videoUrl = videoInfo.video.downloadAddr;
  }

  // 提取封面图
  let coverUrl = "";
  if (videoInfo.video && videoInfo.video.cover) {
    coverUrl = videoInfo.video.cover;
  } else if (videoInfo.video && videoInfo.video.originCover) {
    coverUrl = videoInfo.video.originCover;
  }

  const tiktokVideoInfo = {
    video_url: videoUrl,
    cover_url: coverUrl,
    title: videoInfo.desc || "",
    images: [], // TikTok主要是视频，很少有图集
    author: {
      uid: videoInfo.author?.id || videoInfo.author?.uniqueId || "",
      name: videoInfo.author?.nickname || videoInfo.author?.uniqueId || "TikTok用户",
      avatar: videoInfo.author?.avatarThumb || videoInfo.author?.avatarMedium || "",
    }
  };

  console.log("TikTok视频解析完成:", {
    title: tiktokVideoInfo.title,
    author: tiktokVideoInfo.author.name,
    hasVideo: !!tiktokVideoInfo.video_url
  });

  // 缓存解析结果
  cacheTiktokResult(shareUrl, tiktokVideoInfo);

  return tiktokVideoInfo;
}

// 云函数主入口
exports.main = async (event) => {
  console.log("视频解析云函数被调用:", event);
  
  const { url: inputText, openid, userId } = event;
  
  // 参数验证
  if (!inputText) {
    return {
      code: -1,
      message: "缺少必要参数：url（可以是链接或分享文本）",
    };
  }
  
  if (typeof inputText !== "string" || inputText.trim().length === 0) {
    return {
      code: -1,
      message: "参数url必须是非空字符串",
    };
  }
  
  if (!openid && !userId) {
    return {
      code: -1,
      message: "缺少用户标识参数：openid 或 userId",
    };
  }
  
  // 检测平台类型
  const platform = detectPlatform(inputText);
  
  if (platform === "unknown") {
    return {
      code: -1,
      message: "不支持的平台，目前支持抖音、小红书和TikTok",
    };
  }
  
  try {
    // 用户权限验证（如果提供了用户信息）
    if (openid || userId) {
      const userCollection = db.collection("users");
      const userQuery = openid ? { openid } : { _id: userId };
      const userResult = await userCollection.where(userQuery).get();
      
      if (userResult.data.length === 0) {
        return {
          code: -1,
          message: "用户不存在，请先登录",
        };
      }
    }
    
    let result;

    if (platform === "douyin") {
      // 解析抖音
      const douyinUrl = extractDouyinUrl(inputText);
      if (!douyinUrl) {
        throw new Error("未找到有效的抖音链接");
      }
      result = await parseShareUrl(douyinUrl);
    } else if (platform === "redbook") {
      // 解析小红书
      const redbookUrl = extractRedbookUrl(inputText);
      if (!redbookUrl) {
        throw new Error("未找到有效的小红书链接");
      }
      result = await parseRedbookShareUrl(redbookUrl);
    } else if (platform === "tiktok") {
      // 解析TikTok
      const tiktokUrl = extractTiktokUrl(inputText);
      if (!tiktokUrl) {
        throw new Error("未找到有效的TikTok链接");
      }
      result = await parseTiktokShareUrl(tiktokUrl);
    }
    
    // 检查解析结果是否为视频
    const isVideo = result.video_url && result.video_url.length > 0;
    const hasImages = result.images && result.images.length > 0;
    
    if (!isVideo) {
      const platformName = platform === 'douyin' ? '抖音' : platform === 'redbook' ? '小红书' : 'TikTok';
      return {
        code: -1,
        message: hasImages
          ? `该${platformName}内容是图集，不是视频。本服务仅支持视频处理，请选择包含视频的链接。`
          : `该${platformName}内容没有有效的视频，请检查链接是否正确。`,
        data: {
          platform: platform,
          type: "image_gallery",
          title: result.title,
          author: {
            name: result.author.name,
            uid: result.author.uid,
            avatar: result.author.avatar,
          },
          images: result.images || [],
          reason: "not_video"
        },
      };
    }
    
    // 如果是视频，返回视频信息（不在云函数中下载视频）
    console.log("视频解析成功，返回视频信息...");
    
    return {
      code: 0,
      message: "视频解析成功",
      data: {
        platform: platform,
        type: "video",
        title: result.title,
        author: {
          name: result.author.name,
          uid: result.author.uid,
          avatar: result.author.avatar,
        },
        video: {
          url: result.video_url,
          cover: result.cover_url,
        }
      },
    };
    
  } catch (error) {
    console.error("视频解析失败:", error);
    
    // 根据错误类型返回不同的错误信息
    let errorMessage = "视频解析失败";
    
    if (error.message.includes("未找到有效的抖音链接")) {
      errorMessage = "未找到有效的抖音链接，请检查输入内容";
    } else if (error.message.includes("未找到有效的小红书链接")) {
      errorMessage = "未找到有效的小红书链接，请检查输入内容";
    } else if (error.message.includes("未找到有效的TikTok链接")) {
      errorMessage = "未找到有效的TikTok链接，请检查输入内容";
    } else if (error.message.includes("无法获取重定向URL")) {
      errorMessage = "链接解析失败，请尝试使用最新的分享链接";
    } else if (error.message.includes("parse video json info from html fail")) {
      errorMessage = "页面数据格式已变更，请联系开发者更新解析逻辑";
    } else if (error.message.includes("failed to parse video info from HTML")) {
      errorMessage = "视频不存在或已被删除";
    } else if (error.message.includes("parse fail: note id in response is undefined")) {
      errorMessage = "小红书链接已过期或内容不存在，请使用最新的分享链接";
    } else if (error.message.includes("未获取到小红书页面内容")) {
      errorMessage = "无法访问小红书页面，可能是链接已失效或网络问题";
    } else if (error.message.includes("HTTP请求失败")) {
      errorMessage = "网络请求失败，请稍后重试";
    } else if (error.message.includes("JSON数据解析失败")) {
      errorMessage = "数据解析失败，可能是页面结构发生变化";
    } else if (error.message.includes("failed to parse Videos or Photo Gallery info from json")) {
      errorMessage = "无法解析视频或图集信息，请检查链接是否有效";
    } else if (error.message.includes("视频不存在或已被删除")) {
      errorMessage = "视频不存在或已被删除，请检查链接是否正确";
    } else if (error.message.includes("视频为私密视频，无法访问")) {
      errorMessage = "视频为私密视频，无法访问";
    } else if (error.message.includes("视频未通过审核或已被下架")) {
      errorMessage = "视频未通过审核或已被下架，无法解析";
    } else if (error.message.includes("视频已被删除")) {
      errorMessage = "视频已被删除";
    } else if (error.message.includes("视频已被封禁")) {
      errorMessage = "视频已被封禁，无法访问";
    } else if (error.message.includes("视频无法访问")) {
      errorMessage = "视频无法访问，请检查链接是否有效";
    } else if (error.message.includes("页面结构可能已变更")) {
      errorMessage = "抖音页面结构发生变化，请联系开发者更新解析逻辑";
    } else if (error.message.includes("未获取到TikTok页面内容")) {
      errorMessage = "无法访问TikTok页面，可能是链接已失效、网络问题或地区限制";
    } else if (error.message.includes("TikTok数据解析失败")) {
      errorMessage = "TikTok数据解析失败，页面结构可能已变更，请联系开发者更新解析逻辑";
    } else if (error.message.includes("无法从TikTok页面中提取视频信息")) {
      errorMessage = "无法从TikTok页面中提取视频信息，可能是视频已被删除或设为私密";
    }
    
    return {
      code: -1,
      message: errorMessage,
      error: error.message,
    };
  }
};